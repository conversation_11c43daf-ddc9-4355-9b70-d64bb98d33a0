import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronRight,
  Loader2,
  Monitor,
  Plus,
  Link
} from 'lucide-react';
import { WorkSpace, Website } from '@/types/workspace';
import WebsiteList from './WebsiteList';
import AddWebsiteModal from './AddWebsiteModal';
import {
  WorkspaceItemUserTabs,
  WorkspaceItemTabManager,
  WorkspaceItemBatchOperations,
  WorkspaceItemActions,
  useWebsiteActions
} from './workspace-item';

interface WorkspaceItemProps {
  workspace: WorkSpace;
  isActive: boolean;
  isExpanded: boolean;
  isSetupInProgress?: boolean; // 是否正在后台设置中
  onWorkspaceClick: () => void;
  onToggleExpand: () => void;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onRemoveWebsite: (websiteId: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWebsites: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
}

/**
 * 工作区项目组件 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的892行WorkspaceItem.tsx文件已按照单一职责原则拆分为4个专门的组件：
 * - WorkspaceItemUserTabs: 用户标签页管理（显示/隐藏功能）
 * - WorkspaceItemTabManager: 标签页管理功能（打开全部、固定等）
 * - WorkspaceItemBatchOperations: 批量操作功能（批量选择、操作）
 * - WorkspaceItemActions: 用户操作处理（编辑、删除、添加等）
 * 
 * 📋 向后兼容：
 * 本组件保留了原有的所有功能和接口，确保现有代码无需修改即可使用新的组件结构
 */
const WorkspaceItem: React.FC<WorkspaceItemProps> = ({
  workspace,
  isActive,
  isExpanded,
  isSetupInProgress = false,
  onWorkspaceClick,
  onToggleExpand,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWebsites,

  onTogglePin,
  onBatchPin,
  onBatchDelete
}) => {
  const [batchMode, setBatchMode] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedWebsites, setSelectedWebsites] = useState<Set<string>>(new Set());

  // 使用网站操作钩子
  const {
    handleEditWebsite,
    handleRemoveWebsite,
  } = useWebsiteActions(onUpdateWebsite, onRemoveWebsite);

  // 处理批量选择状态变化
  const handleSelectionChange = (selectedIds: Set<string>) => {
    setSelectedWebsites(selectedIds);
  };

  // 处理批量固定 - 直接实现固定逻辑
  const handleBatchPin = async (websiteIds: string[]) => {
    try {
      console.log(`📌 批量固定网站: ${websiteIds.length} 个`);
      
      for (const websiteId of websiteIds) {
        // 更新网站的固定状态
        onUpdateWebsite(websiteId, { isPinned: true });

        // 查找对应的标签页并固定
        const website = workspace.websites.find(w => w.id === websiteId);
        if (website) {
          await pinWebsiteTabs(website);
        }
      }

      console.log(`✅ 批量固定完成: ${websiteIds.length} 个网站`);
    } catch (error) {
      console.error('❌ 批量固定失败:', error);
    }
  };

  // 处理批量取消固定 - 直接实现取消固定逻辑
  const handleBatchUnpin = async (websiteIds: string[]) => {
    try {
      console.log(`📌 批量取消固定网站: ${websiteIds.length} 个`);
      
      for (const websiteId of websiteIds) {
        // 更新网站的固定状态
        onUpdateWebsite(websiteId, { isPinned: false });

        // 查找对应的标签页并取消固定
        const website = workspace.websites.find(w => w.id === websiteId);
        if (website) {
          await unpinWebsiteTabs(website);
        }
      }

      console.log(`✅ 批量取消固定完成: ${websiteIds.length} 个网站`);
    } catch (error) {
      console.error('❌ 批量取消固定失败:', error);
    }
  };

  // 固定网站对应的标签页（使用Workona ID映射）
  const pinWebsiteTabs = async (website: Website) => {
    try {
      // 通过Workona ID映射查找对应的标签页
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && !tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: true });
                console.log(`📌 固定标签页: ${tab.title}`);
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 固定标签页失败:', error);
    }
  };

  // 取消固定网站对应的标签页（使用Workona ID映射）
  const unpinWebsiteTabs = async (website: Website) => {
    try {
      // 通过Workona ID映射查找对应的标签页
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: false });
                console.log(`📌 取消固定标签页: ${tab.title}`);
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 取消固定标签页失败:', error);
    }
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsite = (url: string) => {
    onAddWebsiteUrl(url);
    setShowAddModal(false);
  };

  /**
   * 处理添加URL按钮点击
   */
  const handleAddWebsiteUrl = () => {
    setShowAddModal(true);
  };

  return (
    <div className={`workspace-item ${isActive ? 'active' : ''}`}>
      {/* 工作区头部 - 左对齐充满布局 */}
      <div className="flex items-center w-full">
        <div
          className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer"
          onClick={onWorkspaceClick}
        >
          {/* 展开/折叠按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand();
            }}
            className="p-1 hover:bg-slate-600 rounded transition-colors duration-150 flex-shrink-0"
          >
            {isExpanded ? (
              <ChevronDown className="w-3.5 h-3.5 text-slate-400" />
            ) : (
              <ChevronRight className="w-3.5 h-3.5 text-slate-400" />
            )}
          </button>

          {/* 工作区图标 */}
          <div
            className="w-7 h-7 rounded flex items-center justify-center text-base flex-shrink-0"
            style={{ backgroundColor: workspace.color + '20', color: workspace.color }}
          >
            {workspace.icon}
          </div>

          {/* 工作区信息 - 充满剩余空间 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-white truncate text-sm">
                {workspace.name}
              </h3>
              {isActive && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" />
                  {isSetupInProgress && (
                    <Loader2 className="w-3 h-3 text-blue-400 animate-spin" />
                  )}
                </div>
              )}
              {/* Workona 风格：显示工作区类型 */}
              {workspace.type && workspace.type !== 'saved' && (
                <span className={`text-xs px-1.5 py-0.5 rounded text-white flex-shrink-0 ${
                  workspace.type === 'temp' ? 'bg-orange-600' : 'bg-blue-600'
                }`}>
                  {workspace.type === 'temp' ? '临时' : '未保存'}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-slate-400">
              <span>{workspace.websites.length} 个网站</span>
            </div>
          </div>

        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center gap-1">
            {/* 添加当前标签页按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onAddCurrentTab();
              }}
              className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
              title="添加当前标签页"
            >
              <Monitor className="w-4 h-4 text-slate-400 hover:text-green-400" />
            </button>

            {/* 用户标签页管理 */}
            <WorkspaceItemUserTabs
              workspace={workspace}
              isActive={isActive}
            />

            {/* 标签页管理 */}
            <WorkspaceItemTabManager
              workspace={workspace}
              isActive={isActive}
              isExpanded={isExpanded}
            />

            {/* 批量操作 */}
            <WorkspaceItemBatchOperations
              workspace={workspace}
              isActive={isActive}
              batchMode={batchMode}
              setBatchMode={setBatchMode}
              selectedWebsites={selectedWebsites}
              onBatchPin={onBatchPin}
              onBatchDelete={onBatchDelete}
              onUpdateWebsite={onUpdateWebsite}
            />

            {/* 用户操作 */}
            <WorkspaceItemActions
              workspace={workspace}
              isActive={isActive}
              onUpdateWorkspace={onUpdateWorkspace}
              onDeleteWorkspace={onDeleteWorkspace}
              onAddCurrentTab={onAddCurrentTab}
              onAddWebsiteUrl={onAddWebsiteUrl}
              onUpdateWebsite={onUpdateWebsite}
              onEnterBatchMode={() => setBatchMode(true)}
            />
        </div>
      </div>

      {/* 网站列表 - 恢复原版本结构 */}
      {isExpanded && workspace.websites.length > 0 && (
        <div className="mt-2">
          <WebsiteList
            websites={workspace.websites}
            activeWorkspace={isActive ? workspace : undefined}
            onEditWebsite={handleEditWebsite}
            onRemoveWebsite={handleRemoveWebsite}
            onReorderWebsites={onReorderWebsites}
            onBatchDelete={onBatchDelete}
            onBatchPin={handleBatchPin}
            onBatchUnpin={handleBatchUnpin}
            onTogglePin={onTogglePin}
            batchMode={batchMode}
            onExitBatchMode={() => setBatchMode(false)}
            onSelectionChange={handleSelectionChange}
          />
        </div>
      )}

      {/* 空状态 - 充满空间 */}
      {isExpanded && workspace.websites.length === 0 && (
        <div className="mt-2 p-3 border-2 border-dashed border-slate-600 rounded text-center">
          <Plus className="w-5 h-5 text-slate-500 mx-auto mb-2" />
          <p className="text-xs text-slate-400 mb-2">
            还没有添加任何网站
          </p>
          <div className="flex gap-1 justify-center">
            <button
              onClick={onAddCurrentTab}
              className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200"
              title="添加当前标签页"
            >
              <Monitor className="w-3.5 h-3.5 text-slate-400" />
            </button>
            <button
              onClick={() => handleAddWebsiteUrl()}
              className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200"
              title="添加URL"
            >
              <Link className="w-3.5 h-3.5 text-slate-400" />
            </button>
          </div>
        </div>
      )}

      {/* 添加网站模态框 */}
      {showAddModal && (
        <AddWebsiteModal
          onClose={() => setShowAddModal(false)}
          onAdd={handleAddWebsite}
        />
      )}
    </div>
  );
};

export default WorkspaceItem;
