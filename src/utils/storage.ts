import {
  WorkSpace,
  Settings,
  StorageData,
  OperationResult,
  TabIdMapping,
  LocalOpenWorkspaces,
  TabGroups,
  WorkspaceSession
} from '@/types/workspace';

/**
 * 存储管理类 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的1,062行storage.ts文件已按照单一职责原则拆分为4个专门的服务类：
 * - StorageCore: 核心存储操作（基础CRUD、设置管理） (./storage/StorageCore.ts)
 * - WorkspaceStorage: 工作区相关存储（工作区数据管理） (./storage/WorkspaceStorage.ts)
 * - WorkonaStorage: Workona格式相关存储（扩展数据管理） (./storage/WorkonaStorage.ts)
 * - StorageImportExport: 数据导入导出（备份恢复） (./storage/StorageImportExport.ts)
 * 
 * 📋 向后兼容：
 * 本文件保留了原有的主要API接口，确保现有代码无需修改即可使用新的模块结构
 */

// 重新导出拆分后的类，保持向后兼容
export {
  StorageCore,
  WorkspaceStorage,
  WorkonaStorage,
  StorageImportExport
} from './storage/index';

// 导入拆分后的类
import {
  StorageCore,
  WorkspaceStorage,
  WorkonaStorage,
  StorageImportExport
} from './storage/index';

/**
 * 存储管理器（主要API类）
 */
export class StorageManager {
  // ==================== 基础存储操作 ====================

  /**
   * 获取所有存储数据
   */
  static async getAllData(): Promise<OperationResult<StorageData>> {
    return StorageImportExport.getAllData();
  }

  /**
   * 获取设置
   */
  static async getSettings(): Promise<OperationResult<Settings>> {
    return StorageCore.getSettings();
  }

  /**
   * 保存设置
   */
  static async saveSettings(settings: Partial<Settings>): Promise<OperationResult<void>> {
    return StorageCore.saveSettings(settings);
  }

  /**
   * 获取活跃工作区ID
   */
  static async getActiveWorkspaceId(): Promise<OperationResult<string | null>> {
    return StorageCore.getActiveWorkspaceId();
  }

  /**
   * 设置活跃工作区ID
   */
  static async setActiveWorkspaceId(id: string | null): Promise<OperationResult<void>> {
    return StorageCore.setActiveWorkspaceId(id);
  }

  /**
   * 更新最近活跃工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId: string): Promise<OperationResult<void>> {
    return StorageCore.updateLastActiveWorkspaces(workspaceId);
  }

  /**
   * 清除所有存储数据
   */
  static async clearAll(): Promise<OperationResult<void>> {
    return StorageCore.clearAll();
  }

  // ==================== 工作区存储操作 ====================

  /**
   * 获取所有工作区
   */
  static async getWorkspaces(): Promise<OperationResult<WorkSpace[]>> {
    return WorkspaceStorage.getWorkspaces();
  }

  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces: WorkSpace[]): Promise<OperationResult<void>> {
    return WorkspaceStorage.saveWorkspaces(workspaces);
  }

  /**
   * 获取单个工作区
   */
  static async getWorkspace(id: string): Promise<OperationResult<WorkSpace>> {
    return WorkspaceStorage.getWorkspace(id);
  }

  /**
   * 更新单个工作区
   */
  static async updateWorkspace(updatedWorkspace: WorkSpace): Promise<OperationResult<void>> {
    return WorkspaceStorage.updateWorkspace(updatedWorkspace);
  }

  // ==================== Workona存储操作 ====================

  /**
   * 保存标签页ID映射
   */
  static async saveTabIdMappings(mappings: TabIdMapping[]): Promise<OperationResult<void>> {
    return WorkonaStorage.saveTabIdMappings(mappings);
  }

  /**
   * 获取标签页ID映射
   */
  static async getTabIdMappings(): Promise<OperationResult<TabIdMapping[]>> {
    return WorkonaStorage.getTabIdMappings();
  }

  /**
   * 保存本地打开工作区
   */
  static async saveLocalOpenWorkspaces(workspaces: LocalOpenWorkspaces): Promise<OperationResult<void>> {
    return WorkonaStorage.saveLocalOpenWorkspaces(workspaces);
  }

  /**
   * 获取本地打开工作区
   */
  static async getLocalOpenWorkspaces(): Promise<OperationResult<LocalOpenWorkspaces>> {
    return WorkonaStorage.getLocalOpenWorkspaces();
  }

  /**
   * 保存标签页组
   */
  static async saveTabGroups(tabGroups: TabGroups): Promise<OperationResult<void>> {
    return WorkonaStorage.saveTabGroups(tabGroups);
  }

  /**
   * 获取标签页组
   */
  static async getTabGroups(): Promise<OperationResult<TabGroups>> {
    return WorkonaStorage.getTabGroups();
  }

  /**
   * 保存工作区会话
   */
  static async saveWorkspaceSessions(sessions: Record<string, WorkspaceSession>): Promise<OperationResult<void>> {
    return WorkonaStorage.saveWorkspaceSessions(sessions);
  }

  /**
   * 获取工作区会话
   */
  static async getWorkspaceSessions(): Promise<OperationResult<Record<string, WorkspaceSession>>> {
    return WorkonaStorage.getWorkspaceSessions();
  }

  /**
   * 保存全局工作区窗口ID
   */
  static async saveGlobalWorkspaceWindowId(windowId: number): Promise<OperationResult<void>> {
    return WorkonaStorage.saveGlobalWorkspaceWindowId(windowId);
  }

  /**
   * 获取全局工作区窗口ID
   */
  static async getGlobalWorkspaceWindowId(): Promise<OperationResult<number | null>> {
    return WorkonaStorage.getGlobalWorkspaceWindowId();
  }

  /**
   * 清除全局工作区窗口ID
   */
  static async clearGlobalWorkspaceWindowId(): Promise<OperationResult<void>> {
    return WorkonaStorage.clearGlobalWorkspaceWindowId();
  }

  /**
   * 保存数据版本
   */
  static async saveDataVersion(version: string): Promise<OperationResult<void>> {
    return WorkonaStorage.saveDataVersion(version);
  }

  /**
   * 获取数据版本
   */
  static async getDataVersion(): Promise<OperationResult<string>> {
    return WorkonaStorage.getDataVersion();
  }

  // ==================== 导入导出操作 ====================

  /**
   * 导出数据为JSON字符串
   */
  static async exportData(): Promise<OperationResult<string>> {
    return StorageImportExport.exportData();
  }

  /**
   * 从JSON字符串导入数据
   */
  static async importData(jsonData: string): Promise<OperationResult<void>> {
    return StorageImportExport.importData(jsonData);
  }

  // ==================== 工具方法 ====================

  /**
   * 添加存储变化监听器
   */
  static onChanged(callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void): void {
    return StorageCore.onChanged(callback);
  }

  /**
   * 获取存储使用情况
   */
  static async getStorageUsage(): Promise<OperationResult<{
    bytesInUse: number;
    quota: number;
    percentUsed: number;
  }>> {
    return StorageCore.getStorageUsage();
  }

  /**
   * 检查存储键是否存在
   */
  static async hasKey(key: string): Promise<OperationResult<boolean>> {
    return StorageCore.hasKey(key);
  }

  /**
   * 删除指定的存储键
   */
  static async removeKey(key: string): Promise<OperationResult<void>> {
    return StorageCore.removeKey(key);
  }

  // ==================== 迁移相关（向后兼容） ====================

  /**
   * 迁移到Workona格式（恢复原始功能）
   */
  static async migrateToWorkonaFormat(): Promise<OperationResult<boolean>> {
    return StorageImportExport.migrateToWorkonaFormat();
  }
}
