import {
  WorkSpace,
  StorageData,
  OperationResult
} from '@/types/workspace';
import {
  STORAGE_KEYS,
  WORKONA_STORAGE_KEYS,
  DEFAULT_SETTINGS,
  ERROR_CODES
} from '../constants';
import { ImportDataProcessor } from '../importDataProcessor';
import { WorkspaceStorage } from './WorkspaceStorage';
import { StorageCore } from './StorageCore';
import { WorkonaStorage } from './WorkonaStorage';

/**
 * 存储导入导出管理器
 * 职责：处理数据的导入导出操作
 * 
 * 🎯 核心职责：
 * 1. 数据导出功能
 * 2. 数据导入功能
 * 3. 数据格式验证
 * 4. 增量导入处理
 * 5. 数据备份和恢复
 */
export class StorageImportExport {
  /**
   * 获取所有存储数据
   */
  static async getAllData(): Promise<OperationResult<StorageData>> {
    try {
      // 获取基础数据和 Workona 扩展数据
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS,
        // Workona 风格数据
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID,
        WORKONA_STORAGE_KEYS.DATA_VERSION,
      ]);

      const data: StorageData = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [],

        // Workona 风格扩展数据（恢复原始默认值处理）
        tabIdMappings: result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [],
        localOpenWorkspaces: result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {},
        tabGroups: result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {},
        workspaceSessions: result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {},
        globalWorkspaceWindowId: result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || undefined,
        dataVersion: result[WORKONA_STORAGE_KEYS.DATA_VERSION] || '1.0.0',
      };

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get all data',
          details: error,
        },
      };
    }
  }

  /**
   * 导出数据为JSON字符串
   */
  static async exportData(): Promise<OperationResult<string>> {
    try {
      const dataResult = await StorageImportExport.getAllData();
      if (!dataResult.success) {
        return { success: false, error: dataResult.error };
      }

      const data = dataResult.data!;

      // 创建导出数据结构
      const exportData = {
        version: '1.0.0',
        exportedAt: Date.now(),
        data: {
          workspaces: data.workspaces,
          settings: data.settings,
          activeWorkspaceId: data.activeWorkspaceId,
          lastActiveWorkspaceIds: data.lastActiveWorkspaceIds,
          // 可选的Workona数据
          ...(data.tabIdMappings && { tabIdMappings: data.tabIdMappings }),
          ...(data.localOpenWorkspaces && { localOpenWorkspaces: data.localOpenWorkspaces }),
          ...(data.tabGroups && { tabGroups: data.tabGroups }),
          ...(data.workspaceSessions && { workspaceSessions: data.workspaceSessions }),
          ...(data.globalWorkspaceWindowId && { globalWorkspaceWindowId: data.globalWorkspaceWindowId }),
          ...(data.dataVersion && { dataVersion: data.dataVersion }),
        },
      };

      const jsonString = JSON.stringify(exportData, null, 2);
      return { success: true, data: jsonString };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.EXPORT_ERROR,
          message: 'Failed to export data',
          details: error,
        },
      };
    }
  }

  /**
   * 从JSON字符串导入数据
   */
  static async importData(jsonData: string): Promise<OperationResult<void>> {
    try {
      // 解析JSON数据
      const importData = JSON.parse(jsonData);

      // 验证数据格式 - 兼容原版本和重构版本
      let dataToImport;
      if (importData.data && importData.data.workspaces) {
        // 重构版本格式：{ data: { workspaces: [...] } }
        dataToImport = importData.data;
      } else if (importData.workspaces) {
        // 原版本格式：{ workspaces: [...] }
        dataToImport = importData;
      } else {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_DATA_FORMAT,
            message: 'Invalid import data format: workspaces array is required',
          },
        };
      }

      // 使用ImportDataProcessor处理导入
      const processResult = await ImportDataProcessor.processImportedData(dataToImport);

      if (!processResult.success) {
        return { success: false, error: processResult.error };
      }

      // 获取现有工作区
      const existingWorkspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!existingWorkspacesResult.success) {
        return { success: false, error: existingWorkspacesResult.error };
      }

      const existingWorkspaces = existingWorkspacesResult.data!;

      // 执行增量导入
      const mergeResult = await StorageImportExport.performIncrementalImport(
        existingWorkspaces,
        dataToImport.workspaces || []
      );

      if (!mergeResult.success) {
        return { success: false, error: mergeResult.error };
      }

      // 保存合并后的工作区
      const saveResult = await WorkspaceStorage.saveWorkspaces(mergeResult.data!);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      // 导入设置（如果存在）
      if (dataToImport.settings) {
        const settingsResult = await StorageCore.saveSettings(dataToImport.settings);
        if (!settingsResult.success) {
          console.warn('导入设置失败:', settingsResult.error);
        }
      }

      // 导入Workona数据（如果存在）
      if (dataToImport.tabIdMappings) {
        await WorkonaStorage.saveTabIdMappings(dataToImport.tabIdMappings);
      }

      if (dataToImport.localOpenWorkspaces) {
        await WorkonaStorage.saveLocalOpenWorkspaces(dataToImport.localOpenWorkspaces);
      }

      if (dataToImport.tabGroups) {
        await WorkonaStorage.saveTabGroups(dataToImport.tabGroups);
      }

      if (dataToImport.workspaceSessions) {
        await WorkonaStorage.saveWorkspaceSessions(dataToImport.workspaceSessions);
      }

      if (dataToImport.dataVersion) {
        await WorkonaStorage.saveDataVersion(dataToImport.dataVersion);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.IMPORT_ERROR,
          message: 'Failed to import data',
          details: error,
        },
      };
    }
  }

  /**
   * 执行增量导入（合并现有数据和导入数据）
   */
  private static async performIncrementalImport(
    existingWorkspaces: WorkSpace[],
    importedWorkspaces: WorkSpace[]
  ): Promise<OperationResult<WorkSpace[]>> {
    try {
      const mergedWorkspaces = [...existingWorkspaces];

      for (const importedWorkspace of importedWorkspaces) {
        // 检查是否已存在同名工作区
        const existingIndex = mergedWorkspaces.findIndex(
          ws => ws.name === importedWorkspace.name
        );

        if (existingIndex >= 0) {
          // 合并现有工作区
          const existingWorkspace = mergedWorkspaces[existingIndex];
          const mergedWebsites = [...existingWorkspace.websites];

          // 添加新网站（避免重复）
          for (const importedWebsite of importedWorkspace.websites) {
            const websiteExists = mergedWebsites.some(
              ws => ws.url === importedWebsite.url
            );

            if (!websiteExists) {
              mergedWebsites.push({
                ...importedWebsite,
                id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                addedAt: Date.now(),
                order: mergedWebsites.length,
              });
            }
          }

          // 更新现有工作区
          mergedWorkspaces[existingIndex] = {
            ...existingWorkspace,
            websites: mergedWebsites,
            updatedAt: Date.now(),
          };
        } else {
          // 添加新工作区
          const newWorkspace: WorkSpace = {
            ...importedWorkspace,
            id: `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            isActive: false,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            order: mergedWorkspaces.length,
            websites: importedWorkspace.websites.map((website, index) => ({
              ...website,
              id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              addedAt: Date.now(),
              order: index,
            })),
          };

          mergedWorkspaces.push(newWorkspace);
        }
      }

      return { success: true, data: mergedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.IMPORT_ERROR,
          message: 'Failed to perform incremental import',
          details: error,
        },
      };
    }
  }

  /**
   * 创建数据备份
   */
  static async createBackup(): Promise<OperationResult<string>> {
    try {
      const exportResult = await StorageImportExport.exportData();
      if (!exportResult.success) {
        return { success: false, error: exportResult.error };
      }

      const backupData = {
        type: 'backup',
        createdAt: Date.now(),
        data: exportResult.data!,
      };

      return { success: true, data: JSON.stringify(backupData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.EXPORT_ERROR,
          message: 'Failed to create backup',
          details: error,
        },
      };
    }
  }

  /**
   * 从备份恢复数据
   */
  static async restoreFromBackup(backupData: string): Promise<OperationResult<void>> {
    try {
      const backup = JSON.parse(backupData);

      if (backup.type !== 'backup' || !backup.data) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_DATA_FORMAT,
            message: 'Invalid backup data format',
          },
        };
      }

      return await StorageImportExport.importData(backup.data);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.IMPORT_ERROR,
          message: 'Failed to restore from backup',
          details: error,
        },
      };
    }
  }

  /**
   * 渐进式数据迁移检查（恢复原始功能）
   * 检测现有数据格式，逐步迁移到 Workona 格式
   */
  static async migrateToWorkonaFormat(): Promise<OperationResult<boolean>> {
    try {
      console.log('🔄 开始检查 Workona 数据迁移需求...');

      // 检查当前数据版本
      const versionResult = await WorkonaStorage.getDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }

      const currentVersion = versionResult.data!;
      const targetVersion = '1.0.0';

      // 如果版本已经是最新，无需迁移
      if (currentVersion === targetVersion) {
        console.log('✅ 数据版本已是最新，无需迁移');
        return { success: true, data: false };
      }

      console.log(`📦 检测到数据版本 ${currentVersion}，开始迁移到 ${targetVersion}...`);

      // 获取现有数据
      const allDataResult = await this.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }

      const data = allDataResult.data!;

      // 如果 Workona 数据不存在，初始化空数据
      if (!data.tabIdMappings || data.tabIdMappings.length === 0) {
        await WorkonaStorage.saveTabIdMappings([]);
      }

      if (!data.localOpenWorkspaces || Object.keys(data.localOpenWorkspaces).length === 0) {
        await WorkonaStorage.saveLocalOpenWorkspaces({});
      }

      if (!data.tabGroups || Object.keys(data.tabGroups).length === 0) {
        await WorkonaStorage.saveTabGroups({});
      }

      if (!data.workspaceSessions || Object.keys(data.workspaceSessions).length === 0) {
        await WorkonaStorage.saveWorkspaceSessions({});
      }

      // 更新数据版本
      await WorkonaStorage.saveDataVersion(targetVersion);

      console.log('✅ Workona 数据迁移完成');
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ Workona 数据迁移失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to migrate to Workona format',
          details: error,
        },
      };
    }
  }
}
