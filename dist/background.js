import { S as StorageManager, C as COMMANDS, c as WorkspaceSwitcher, U as UserTabsRealTimeMonitor, b as WorkonaTabManager, f as WorkspaceSessionManager, g as TabClassificationService, M as MigrationManager } from './assets/dataMigration-C6zv6U5p.js';

class ServiceWorkerManager {
  static isFirstRealStartup = true;
  static lastInitTimestamp = 0;
  static SERVICE_WORKER_WAKEUP_THRESHOLD = 5 * 60 * 1e3;
  // 5分钟
  /**
   * 智能工作区状态管理
   */
  static async smartWorkspaceStateManagement() {
    try {
      const now = Date.now();
      const timeSinceLastInit = now - ServiceWorkerManager.lastInitTimestamp;
      if (timeSinceLastInit < ServiceWorkerManager.SERVICE_WORKER_WAKEUP_THRESHOLD && !ServiceWorkerManager.isFirstRealStartup) {
        console.log("🔄 检测到Service Worker唤醒，保持工作区状态");
        ServiceWorkerManager.lastInitTimestamp = now;
        return;
      }
      console.log("🔄 检测到真正的启动事件 - 浏览器重启或扩展首次加载");
      console.log("🔄 根据用户需求，重置工作区选择状态");
      await WorkspaceStateManager.resetWorkspaceStateOnBrowserRestart();
      ServiceWorkerManager.isFirstRealStartup = false;
      ServiceWorkerManager.lastInitTimestamp = now;
    } catch (error) {
      console.error("智能工作区状态管理失败:", error);
    }
  }
  /**
   * 设置Chrome运行时事件监听器
   */
  static setupRuntimeListeners() {
    if (chrome.runtime.onStartup) {
      chrome.runtime.onStartup.addListener(async () => {
        console.log("🚀 Chrome扩展启动事件触发 - 浏览器重启");
        await ServiceWorkerManager.smartWorkspaceStateManagement();
      });
    } else {
      console.warn("⚠️ chrome.runtime.onStartup 不可用");
    }
    if (chrome.runtime.onInstalled) {
      chrome.runtime.onInstalled.addListener(async (details) => {
        console.log("📦 Chrome扩展安装/更新事件:", details.reason);
        if (details.reason === "install") {
          console.log("🎉 扩展首次安装");
          await ServiceWorkerManager.smartWorkspaceStateManagement();
        } else if (details.reason === "update") {
          console.log("🔄 扩展更新");
        }
      });
    } else {
      console.warn("⚠️ chrome.runtime.onInstalled 不可用");
    }
  }
  /**
   * 获取Service Worker状态信息
   */
  static getServiceWorkerInfo() {
    return {
      isFirstRealStartup: ServiceWorkerManager.isFirstRealStartup,
      lastInitTimestamp: ServiceWorkerManager.lastInitTimestamp,
      timeSinceLastInit: Date.now() - ServiceWorkerManager.lastInitTimestamp
    };
  }
  /**
   * 重置Service Worker状态
   */
  static resetServiceWorkerState() {
    ServiceWorkerManager.isFirstRealStartup = true;
    ServiceWorkerManager.lastInitTimestamp = 0;
    console.log("🔄 Service Worker状态已重置");
  }
}
class WorkspaceStateManager {
  /**
   * 浏览器重启后重置工作区状态
   */
  static async resetWorkspaceStateOnBrowserRestart() {
    try {
      console.log("🔄 浏览器重启检测 - 重置工作区选择状态");
      const result = await StorageManager.setActiveWorkspaceId(null);
      if (result.success) {
        console.log("✅ 工作区状态已重置为未选择状态");
      } else {
        console.error("❌ 重置工作区状态失败:", result.error);
      }
    } catch (error) {
      console.error("重置工作区状态时发生错误:", error);
    }
  }
  /**
   * 检查是否需要重置工作区状态
   */
  static async shouldResetWorkspaceState() {
    try {
      const activeWorkspaceResult = await StorageManager.getActiveWorkspaceId();
      if (!activeWorkspaceResult.success) {
        return false;
      }
      const activeWorkspaceId = activeWorkspaceResult.data;
      if (activeWorkspaceId) {
        console.log(`当前活跃工作区: ${activeWorkspaceId}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error("检查工作区状态时发生错误:", error);
      return false;
    }
  }
  /**
   * 获取工作区状态信息
   */
  static async getWorkspaceStateInfo() {
    try {
      const activeWorkspaceResult = await StorageManager.getActiveWorkspaceId();
      const activeWorkspaceId = activeWorkspaceResult.success ? activeWorkspaceResult.data : null;
      const shouldReset = await WorkspaceStateManager.shouldResetWorkspaceState();
      return {
        hasActiveWorkspace: !!activeWorkspaceId,
        activeWorkspaceId: activeWorkspaceId || null,
        shouldReset
      };
    } catch (error) {
      console.error("获取工作区状态信息失败:", error);
      return {
        hasActiveWorkspace: false,
        activeWorkspaceId: null,
        shouldReset: false
      };
    }
  }
}

class BackgroundEventHandlers {
  /**
   * 设置侧边栏
   */
  static async setupSidePanel() {
    try {
      if (chrome.sidePanel) {
        await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
        console.log("✅ 侧边栏设置完成");
      } else {
        console.warn("⚠️ chrome.sidePanel API 不可用");
      }
    } catch (error) {
      console.error("设置侧边栏失败:", error);
    }
  }
  /**
   * 设置命令监听器
   */
  static setupCommandListeners() {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log("Command received:", command);
      try {
        switch (command) {
          case COMMANDS.TOGGLE_SIDEPANEL:
            await BackgroundEventHandlers.toggleSidePanel();
            break;
          case COMMANDS.SWITCH_WORKSPACE_1:
            await BackgroundEventHandlers.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await BackgroundEventHandlers.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await BackgroundEventHandlers.switchToWorkspaceByIndex(2);
            break;
          default:
            console.log("未知命令:", command);
        }
      } catch (error) {
        console.error("处理命令时发生错误:", error);
      }
    });
  }
  /**
   * 设置存储监听器
   */
  static setupStorageListeners() {
    StorageManager.onChanged((changes) => {
      BackgroundEventHandlers.notifySidePanelUpdate(changes);
    });
  }
  /**
   * 切换到指定索引的工作区
   */
  static async switchToWorkspaceByIndex(index) {
    try {
      console.log(`🔄 切换到工作区索引: ${index}`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error("获取工作区列表失败:", workspacesResult.error);
        return;
      }
      const workspaces = workspacesResult.data;
      if (index >= workspaces.length) {
        console.log(`工作区索引 ${index} 超出范围，总共有 ${workspaces.length} 个工作区`);
        return;
      }
      const targetWorkspace = workspaces[index];
      console.log(`切换到工作区: ${targetWorkspace.name}`);
      const switchResult = await WorkspaceSwitcher.switchToWorkspace(targetWorkspace.id);
      if (switchResult.success) {
        console.log(`✅ 成功切换到工作区: ${targetWorkspace.name}`);
      } else {
        console.error("工作区切换失败:", switchResult.error);
      }
    } catch (error) {
      console.error("切换工作区时发生错误:", error);
    }
  }
  /**
   * 切换侧边栏显示状态
   */
  static async toggleSidePanel() {
    try {
      if (chrome.sidePanel) {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length > 0) {
          const tabId = tabs[0].id;
          await chrome.sidePanel.open({ tabId });
          console.log("✅ 侧边栏已打开");
        }
      } else {
        console.warn("⚠️ chrome.sidePanel API 不可用");
      }
    } catch (error) {
      console.error("切换侧边栏失败:", error);
    }
  }
  /**
   * 通知侧边栏更新
   */
  static notifySidePanelUpdate(changes) {
    try {
      chrome.runtime.sendMessage({
        type: "STORAGE_CHANGED",
        changes
      }).catch(() => {
      });
    } catch (error) {
      console.error("通知侧边栏更新失败:", error);
    }
  }
  /**
   * 显示通知
   */
  static showNotification(message, icon) {
    try {
      if (chrome.notifications) {
        chrome.notifications.create({
          type: "basic",
          iconUrl: icon || "/icons/icon-48.png",
          title: "Workspace Pro",
          message
        });
      } else {
        console.log("通知:", message);
      }
    } catch (error) {
      console.error("显示通知失败:", error);
    }
  }
  /**
   * 获取事件处理器状态
   */
  static getEventHandlerStatus() {
    return {
      commandListenersActive: !!chrome.commands?.onCommand,
      storageListenersActive: !!chrome.storage?.onChanged,
      sidePanelSupported: !!chrome.sidePanel,
      notificationsSupported: !!chrome.notifications
    };
  }
  /**
   * 初始化所有事件监听器
   */
  static async initializeAllEventHandlers() {
    try {
      console.log("🔧 初始化事件处理器...");
      await BackgroundEventHandlers.setupSidePanel();
      BackgroundEventHandlers.setupCommandListeners();
      BackgroundEventHandlers.setupStorageListeners();
      console.log("✅ 所有事件处理器初始化完成");
    } catch (error) {
      console.error("初始化事件处理器失败:", error);
    }
  }
}

class BackgroundTabManager {
  /**
   * 启动用户标签页实时监控
   */
  static async startUserTabsRealTimeMonitoring() {
    try {
      console.log("🔄 启动用户标签页实时监控...");
      await UserTabsRealTimeMonitor.startMonitoring();
      console.log("✅ 用户标签页实时监控已启动");
    } catch (error) {
      console.error("启动用户标签页实时监控失败:", error);
    }
  }
  /**
   * 刷新用户标签页监控
   */
  static async refreshUserTabsMonitoring() {
    try {
      console.log("🔄 刷新用户标签页监控状态...");
      await UserTabsRealTimeMonitor.stopMonitoring();
      await BackgroundTabManager.startUserTabsRealTimeMonitoring();
      console.log("✅ 用户标签页监控已刷新");
    } catch (error) {
      console.error("刷新用户标签页监控失败:", error);
    }
  }
  /**
   * 标签页编辑后同步
   */
  static async syncTabAfterEdit(tabId, changeInfo, _tab) {
    try {
      if (changeInfo.url || changeInfo.title) {
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          console.log(`🔄 同步标签页变化: ${tabId} -> ${workonaIdResult.data}`);
        }
      }
    } catch (error) {
      console.error("同步标签页编辑失败:", error);
    }
  }
  /**
   * 处理标签页固定状态变化
   */
  static async handleTabPinnedStateChange(tabId, isPinned, _tab) {
    try {
      console.log(`🔄 标签页固定状态变化: ${tabId} -> ${isPinned ? "固定" : "取消固定"}`);
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return;
      }
      const workonaId = workonaIdResult.data;
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return;
      }
      const { workspaceId } = metadataResult.data;
      if (!workspaceId) {
        return;
      }
      if (isPinned) {
        await BackgroundTabManager.updateWorkspacePinnedTabIds(workspaceId, tabId);
      } else {
        await BackgroundTabManager.removeFromWorkspacePinnedTabIds(workspaceId, tabId);
      }
    } catch (error) {
      console.error("处理标签页固定状态变化失败:", error);
    }
  }
  /**
   * 恢复标签页映射（浏览器重启后）
   */
  static async restoreTabMappingsAfterRestart() {
    try {
      console.log("🔄 浏览器重启后恢复标签页映射...");
      const tabs = await chrome.tabs.query({});
      console.log(`发现 ${tabs.length} 个标签页`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error("获取工作区失败:", workspacesResult.error);
        return;
      }
      const workspaces = workspacesResult.data;
      let restoredCount = 0;
      for (const workspace of workspaces) {
        for (const website of workspace.websites) {
          const matchingTabs = tabs.filter(
            (tab) => tab.url && tab.url.includes(new URL(website.url).hostname)
          );
          for (const tab of matchingTabs) {
            if (!tab.id) continue;
            const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
            if (existingResult.success && existingResult.data) {
              continue;
            }
            const workonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaId,
              tab.id,
              workspace.id,
              website.id,
              {
                isWorkspaceCore: true,
                source: "session_restored"
              }
            );
            if (mappingResult.success) {
              restoredCount++;
              console.log(`✅ 恢复标签页映射: ${tab.title} -> ${workonaId}`);
            }
          }
        }
      }
      console.log(`✅ 标签页映射恢复完成，共恢复 ${restoredCount} 个映射`);
    } catch (error) {
      console.error("恢复标签页映射失败:", error);
    }
  }
  /**
   * 更新工作区固定标签页ID
   */
  static async updateWorkspacePinnedTabIds(workspaceId, newChromeId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return;
      }
      const workspace = workspaceResult.data;
      const pinnedTabIds = workspace.pinnedTabIds || [];
      if (!pinnedTabIds.includes(newChromeId)) {
        pinnedTabIds.push(newChromeId);
        const updatedWorkspace = {
          ...workspace,
          pinnedTabIds,
          updatedAt: Date.now()
        };
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success) {
          const workspaces = workspacesResult.data;
          const workspaceIndex = workspaces.findIndex((w) => w.id === workspaceId);
          if (workspaceIndex !== -1) {
            workspaces[workspaceIndex] = updatedWorkspace;
            await StorageManager.saveWorkspaces(workspaces);
          }
        }
      }
    } catch (error) {
      console.error("更新工作区固定标签页ID失败:", error);
    }
  }
  /**
   * 从工作区固定标签页ID中移除
   */
  static async removeFromWorkspacePinnedTabIds(workspaceId, chromeId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return;
      }
      const workspace = workspaceResult.data;
      const pinnedTabIds = workspace.pinnedTabIds || [];
      const updatedPinnedTabIds = pinnedTabIds.filter((id) => id !== chromeId);
      if (updatedPinnedTabIds.length !== pinnedTabIds.length) {
        const updatedWorkspace = {
          ...workspace,
          pinnedTabIds: updatedPinnedTabIds,
          updatedAt: Date.now()
        };
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success) {
          const workspaces = workspacesResult.data;
          const workspaceIndex = workspaces.findIndex((w) => w.id === workspaceId);
          if (workspaceIndex !== -1) {
            workspaces[workspaceIndex] = updatedWorkspace;
            await StorageManager.saveWorkspaces(workspaces);
          }
        }
      }
    } catch (error) {
      console.error("从工作区固定标签页ID中移除失败:", error);
    }
  }
  /**
   * 清理所有工作区的固定状态
   */
  static async cleanupAllWorkspacePinnedStates() {
    try {
      console.log("🧹 清理所有工作区的固定标签页状态...");
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return;
      }
      const workspaces = workspacesResult.data;
      let cleanedCount = 0;
      for (const workspace of workspaces) {
        if (workspace.pinnedTabIds && workspace.pinnedTabIds.length > 0) {
          workspace.pinnedTabIds = [];
          workspace.updatedAt = Date.now();
          cleanedCount++;
        }
      }
      if (cleanedCount > 0) {
        await StorageManager.saveWorkspaces(workspaces);
        console.log(`✅ 已清理 ${cleanedCount} 个工作区的固定标签页状态`);
      }
    } catch (error) {
      console.error("清理工作区固定状态失败:", error);
    }
  }
  /**
   * 设置标签页事件监听器
   */
  static setupTabListeners() {
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        console.log(`标签页激活: ${activeInfo.tabId}`);
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeInfo.tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          console.log(`活跃标签页Workona ID: ${workonaIdResult.data}`);
        }
      } catch (error) {
        console.error("处理标签页激活事件失败:", error);
      }
    });
    chrome.tabs.onMoved.addListener(async (_tabId, _moveInfo) => {
      try {
        setTimeout(async () => {
          await WorkspaceSessionManager.syncCurrentWorkspaceState();
        }, 100);
      } catch (error) {
        console.error("处理标签页移动事件失败:", error);
      }
    });
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log(`新标签页创建: ${tab.id} - ${tab.url}`);
        if (!tab.url || tab.url === "chrome://newtab/" || tab.url === "about:blank") {
          return;
        }
        setTimeout(async () => {
          try {
            await TabClassificationService.autoClassifyNewTab(tab.id, tab.url);
          } catch (error) {
            console.error("自动分类标签页失败:", error);
          }
        }, 1e3);
      } catch (error) {
        console.error("处理标签页创建事件失败:", error);
      }
    });
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      try {
        if (changeInfo.pinned !== void 0) {
          await BackgroundTabManager.handleTabPinnedStateChange(tabId, changeInfo.pinned, tab);
        }
        if (changeInfo.url || changeInfo.title) {
          await BackgroundTabManager.syncTabAfterEdit(tabId, changeInfo, tab);
        }
        if (changeInfo.status === "complete" && tab.url && !tab.url.startsWith("chrome://") && !tab.url.startsWith("chrome-extension://")) {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
          if (!workonaIdResult.success || !workonaIdResult.data) {
            setTimeout(async () => {
              try {
                await TabClassificationService.autoClassifyNewTab(tabId, tab.url);
              } catch (error) {
                console.error("自动分类标签页失败:", error);
              }
            }, 500);
          }
        }
      } catch (error) {
        console.error("处理标签页更新事件失败:", error);
      }
    });
    chrome.tabs.onRemoved.addListener(async (tabId, _removeInfo) => {
      try {
        console.log(`标签页移除: ${tabId}`);
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workonaId = workonaIdResult.data;
          console.log(`清理Workona ID映射: ${workonaId}`);
          await WorkonaTabManager.removeTabMapping(workonaId);
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data) {
            const { workspaceId } = metadataResult.data;
            if (workspaceId) {
            }
          }
        }
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      } catch (error) {
        console.error("处理标签页移除事件失败:", error);
      }
    });
  }
  /**
   * 获取标签页管理器状态
   */
  static async getTabManagerStatus() {
    try {
      const tabs = await chrome.tabs.query({});
      const pinnedTabs = tabs.filter((tab) => tab.pinned);
      let mappedCount = 0;
      for (const tab of tabs) {
        if (tab.id) {
          const result = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (result.success && result.data) {
            mappedCount++;
          }
        }
      }
      return {
        monitoringActive: UserTabsRealTimeMonitor.getMonitoringStatus().isMonitoring,
        totalTabs: tabs.length,
        mappedTabs: mappedCount,
        pinnedTabs: pinnedTabs.length
      };
    } catch (error) {
      console.error("获取标签页管理器状态失败:", error);
      return {
        monitoringActive: false,
        totalTabs: 0,
        mappedTabs: 0,
        pinnedTabs: 0
      };
    }
  }
}

class BackgroundNotificationManager {
  /**
   * 显示系统通知
   */
  static showNotification(message, icon) {
    try {
      if (chrome.notifications) {
        chrome.notifications.create({
          type: "basic",
          iconUrl: icon || "/icons/icon-48.png",
          title: "Workspace Pro",
          message
        });
      } else {
        console.log("通知:", message);
      }
    } catch (error) {
      console.error("显示通知失败:", error);
    }
  }
  /**
   * 通知全局用户标签页状态变化
   */
  static async notifyGlobalUserTabsStateChange(eventType) {
    try {
      const sendNotification = async () => {
        try {
          await chrome.runtime.sendMessage({
            type: "USER_TABS_STATE_CHANGED",
            eventType,
            timestamp: Date.now()
          });
        } catch (error) {
        }
      };
      await sendNotification();
      setTimeout(async () => {
        await sendNotification();
      }, 100);
      console.log(`📢 已发送用户标签页状态变化通知: ${eventType}`);
    } catch (error) {
      console.error("发送用户标签页状态变化通知失败:", error);
    }
  }
  /**
   * 通知工作区状态变化
   */
  static async notifyWorkspaceStateChange(workspaceId, eventType, data) {
    try {
      await chrome.runtime.sendMessage({
        type: "WORKSPACE_STATE_CHANGED",
        workspaceId,
        eventType,
        data,
        timestamp: Date.now()
      });
      console.log(`📢 已发送工作区状态变化通知: ${workspaceId} - ${eventType}`);
    } catch (error) {
    }
  }
  /**
   * 通知标签页状态变化
   */
  static async notifyTabStateChange(tabId, eventType, data) {
    try {
      await chrome.runtime.sendMessage({
        type: "TAB_STATE_CHANGED",
        tabId,
        eventType,
        data,
        timestamp: Date.now()
      });
      console.log(`📢 已发送标签页状态变化通知: ${tabId} - ${eventType}`);
    } catch (error) {
    }
  }
  /**
   * 通知侧边栏数据更新
   */
  static notifySidePanelUpdate(changes) {
    try {
      chrome.runtime.sendMessage({
        type: "STORAGE_CHANGED",
        changes,
        timestamp: Date.now()
      }).catch(() => {
      });
    } catch (error) {
      console.error("通知侧边栏更新失败:", error);
    }
  }
  /**
   * 发送自定义消息
   */
  static async sendCustomMessage(type, data) {
    try {
      await chrome.runtime.sendMessage({
        type,
        data,
        timestamp: Date.now()
      });
      console.log(`📢 已发送自定义消息: ${type}`);
    } catch (error) {
    }
  }
  /**
   * 批量发送通知
   */
  static async sendBatchNotifications(notifications) {
    try {
      for (const notification of notifications) {
        await BackgroundNotificationManager.sendCustomMessage(notification.type, notification.data);
        await new Promise((resolve) => setTimeout(resolve, 10));
      }
      console.log(`📢 已发送 ${notifications.length} 个批量通知`);
    } catch (error) {
      console.error("发送批量通知失败:", error);
    }
  }
  /**
   * 显示成功通知
   */
  static showSuccessNotification(message) {
    BackgroundNotificationManager.showNotification(`✅ ${message}`, "/icons/success.png");
  }
  /**
   * 显示错误通知
   */
  static showErrorNotification(message) {
    BackgroundNotificationManager.showNotification(`❌ ${message}`, "/icons/error.png");
  }
  /**
   * 显示警告通知
   */
  static showWarningNotification(message) {
    BackgroundNotificationManager.showNotification(`⚠️ ${message}`, "/icons/warning.png");
  }
  /**
   * 显示信息通知
   */
  static showInfoNotification(message) {
    BackgroundNotificationManager.showNotification(`ℹ️ ${message}`, "/icons/info.png");
  }
  /**
   * 获取通知管理器状态
   */
  static getNotificationManagerStatus() {
    return {
      notificationsSupported: !!chrome.notifications,
      runtimeMessagingSupported: !!chrome.runtime?.sendMessage,
      lastNotificationTime: null
      // 可以添加时间戳跟踪
    };
  }
  /**
   * 清理通知历史
   */
  static async clearNotificationHistory() {
    try {
      if (chrome.notifications && chrome.notifications.clear) {
        chrome.notifications.getAll((notifications) => {
          for (const notificationId in notifications) {
            chrome.notifications.clear(notificationId);
          }
        });
        console.log("✅ 通知历史已清理");
      }
    } catch (error) {
      console.error("清理通知历史失败:", error);
    }
  }
  /**
   * 设置通知点击监听器
   */
  static setupNotificationListeners() {
    if (chrome.notifications && chrome.notifications.onClicked) {
      chrome.notifications.onClicked.addListener((notificationId) => {
        console.log(`通知被点击: ${notificationId}`);
      });
    }
    if (chrome.notifications && chrome.notifications.onClosed) {
      chrome.notifications.onClosed.addListener((notificationId, byUser) => {
        console.log(`通知被关闭: ${notificationId}, 用户操作: ${byUser}`);
      });
    }
  }
  /**
   * 初始化通知管理器
   */
  static initializeNotificationManager() {
    try {
      console.log("🔧 初始化通知管理器...");
      BackgroundNotificationManager.setupNotificationListeners();
      console.log("✅ 通知管理器初始化完成");
    } catch (error) {
      console.error("初始化通知管理器失败:", error);
    }
  }
}

class BackgroundCore {
  static isInitialized = false;
  static initStartTime = 0;
  /**
   * 初始化后台服务
   */
  static async initialize() {
    if (BackgroundCore.isInitialized) {
      console.log("⚠️ 后台服务已初始化，跳过重复初始化");
      return;
    }
    BackgroundCore.initStartTime = Date.now();
    console.log("🚀 开始初始化后台服务...");
    try {
      await BackgroundCore.setupBasicFeatures();
      await BackgroundCore.initializeData();
      await BackgroundCore.startMonitoringServices();
      ServiceWorkerManager.setupRuntimeListeners();
      await ServiceWorkerManager.smartWorkspaceStateManagement();
      BackgroundCore.isInitialized = true;
      const initTime = Date.now() - BackgroundCore.initStartTime;
      console.log(`✅ 后台服务初始化完成，耗时: ${initTime}ms`);
    } catch (error) {
      console.error("❌ 后台服务初始化失败:", error);
      throw error;
    }
  }
  /**
   * 设置基础功能
   */
  static async setupBasicFeatures() {
    console.log("🔧 设置基础功能...");
    try {
      await BackgroundEventHandlers.initializeAllEventHandlers();
      BackgroundTabManager.setupTabListeners();
      BackgroundNotificationManager.initializeNotificationManager();
      console.log("✅ 基础功能设置完成");
    } catch (error) {
      console.error("设置基础功能失败:", error);
      throw error;
    }
  }
  /**
   * 初始化数据
   */
  static async initializeData() {
    console.log("📊 初始化数据...");
    try {
      await BackgroundCore.checkAndMigrateData();
      await BackgroundCore.initializeDefaultData();
      await BackgroundTabManager.restoreTabMappingsAfterRestart();
      console.log("✅ 数据初始化完成");
    } catch (error) {
      console.error("数据初始化失败:", error);
      throw error;
    }
  }
  /**
   * 启动监控服务
   */
  static async startMonitoringServices() {
    console.log("👁️ 启动监控服务...");
    try {
      await BackgroundTabManager.startUserTabsRealTimeMonitoring();
      console.log("✅ 监控服务启动完成");
    } catch (error) {
      console.error("启动监控服务失败:", error);
      throw error;
    }
  }
  /**
   * 检查并执行数据迁移
   */
  static async checkAndMigrateData() {
    try {
      console.log("🔄 检查数据迁移需求...");
      const migrationResult = await MigrationManager.migrateToWorkonaFormat();
      if (migrationResult.success) {
        if (migrationResult.data) {
          console.log(`✅ 数据迁移完成`);
          BackgroundNotificationManager.showSuccessNotification("数据迁移完成");
        } else {
          console.log("ℹ️ 无需数据迁移");
        }
      } else {
        console.error("数据迁移失败:", migrationResult.error);
        BackgroundNotificationManager.showErrorNotification("数据迁移失败，请检查扩展状态");
      }
    } catch (error) {
      console.error("检查数据迁移时发生错误:", error);
    }
  }
  /**
   * 初始化默认数据
   */
  static async initializeDefaultData() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data.length === 0) {
        console.log("🆕 创建默认工作区...");
      }
    } catch (error) {
      console.error("初始化默认数据失败:", error);
    }
  }
  /**
   * 重启后台服务
   */
  static async restart() {
    console.log("🔄 重启后台服务...");
    try {
      BackgroundCore.isInitialized = false;
      await BackgroundCore.initialize();
      console.log("✅ 后台服务重启完成");
      BackgroundNotificationManager.showInfoNotification("后台服务已重启");
    } catch (error) {
      console.error("重启后台服务失败:", error);
      BackgroundNotificationManager.showErrorNotification("后台服务重启失败");
    }
  }
  /**
   * 获取后台服务状态
   */
  static async getServiceStatus() {
    try {
      const [tabManagerStatus] = await Promise.all([
        BackgroundTabManager.getTabManagerStatus()
      ]);
      return {
        isInitialized: BackgroundCore.isInitialized,
        initTime: BackgroundCore.initStartTime,
        serviceWorkerInfo: ServiceWorkerManager.getServiceWorkerInfo(),
        eventHandlerStatus: BackgroundEventHandlers.getEventHandlerStatus(),
        tabManagerStatus,
        notificationManagerStatus: BackgroundNotificationManager.getNotificationManagerStatus()
      };
    } catch (error) {
      console.error("获取服务状态失败:", error);
      return {
        isInitialized: false,
        initTime: 0,
        serviceWorkerInfo: null,
        eventHandlerStatus: null,
        tabManagerStatus: null,
        notificationManagerStatus: null
      };
    }
  }
  /**
   * 清理后台服务
   */
  static async cleanup() {
    console.log("🧹 清理后台服务...");
    try {
      await BackgroundTabManager.cleanupAllWorkspacePinnedStates();
      await BackgroundNotificationManager.clearNotificationHistory();
      ServiceWorkerManager.resetServiceWorkerState();
      console.log("✅ 后台服务清理完成");
    } catch (error) {
      console.error("清理后台服务失败:", error);
    }
  }
  /**
   * 检查服务健康状态
   */
  static async healthCheck() {
    const issues = [];
    const recommendations = [];
    try {
      if (!BackgroundCore.isInitialized) {
        issues.push("后台服务未初始化");
        recommendations.push("重新启动扩展");
      }
      const tabStatus = await BackgroundTabManager.getTabManagerStatus();
      if (!tabStatus.monitoringActive) {
        issues.push("标签页监控未激活");
        recommendations.push("刷新标签页监控");
      }
      const eventStatus = BackgroundEventHandlers.getEventHandlerStatus();
      if (!eventStatus.commandListenersActive) {
        issues.push("命令监听器未激活");
        recommendations.push("检查扩展权限");
      }
      return {
        healthy: issues.length === 0,
        issues,
        recommendations
      };
    } catch (error) {
      console.error("健康检查失败:", error);
      return {
        healthy: false,
        issues: ["健康检查执行失败"],
        recommendations: ["重新启动扩展"]
      };
    }
  }
}

if (typeof window === "undefined") {
  globalThis.window = {
    dispatchEvent: (event) => {
      console.log("Service Worker: 忽略 window.dispatchEvent 调用:", event.type);
      return true;
    }
  };
}
class BackgroundService {
  constructor() {
    this.init();
  }
  /**
   * 初始化后台服务
   */
  async init() {
    try {
      console.log("🚀 启动后台服务...");
      await BackgroundCore.initialize();
      console.log("✅ 后台服务启动完成");
    } catch (error) {
      console.error("❌ 后台服务启动失败:", error);
      setTimeout(async () => {
        console.log("🔄 尝试重启后台服务...");
        try {
          await BackgroundCore.restart();
        } catch (restartError) {
          console.error("❌ 重启失败:", restartError);
        }
      }, 5e3);
    }
  }
}
new BackgroundService();
